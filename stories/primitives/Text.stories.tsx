import React from 'react'

import { StoryFn, Meta } from '@storybook/react'

import { Text, TextProps } from '../../src/primitives/Text'
import { style } from '../../src/primitives/Style'

const Template: StoryFn<TextProps> = (props: TextProps) => <Text {...props} />

export default {
  title: 'Primitives/Text',
  component: Text,
} as Meta<typeof Text>

export const Default = Template.bind({})
Default.storyName = 'Default'
Default.args = { children: 'My text' }

export const LongText = Template.bind({})
LongText.storyName = 'With long text'
LongText.args = { children: 'My very very too long text' }

export const CustomStyle = Template.bind({})
CustomStyle.storyName = 'With custom style'
CustomStyle.args = {
  children: 'My text',
  style: style({
    color: 'red',
    fontWeight: '_600',
    fontSize: 24,
    fontStyle: 'italic',
    letterSpacing: 2,
  }),
}
