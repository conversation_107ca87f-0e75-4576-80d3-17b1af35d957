import React from 'react'

import { StoryFn, Meta } from '@storybook/react'

import {
  Image,
  ImageProps,
  imageUriSource,
  imageFromUriSource,
} from '../../src/primitives/Image'

// @ts-expect-error
import testImage from './image.jpeg'

const Template: StoryFn<ImageProps> = (props: ImageProps) => <Image {...props} />

export default {
  title: 'Primitives/Image',
  component: Image,
} as Meta<typeof Image>

const source = imageFromUriSource(imageUriSource(testImage))

export const Default = Template.bind({})
Default.storyName = 'Default'
Default.args = {
  source,
  style: { width: 100, height: 100 },
}

export const DefaultWithStyle = Template.bind({})
DefaultWithStyle.storyName = 'Default with style'
DefaultWithStyle.args = {
  source,
  style: {
    width: 250,
    height: 250,
    borderColor: 'red',
    borderStyle: 'solid',
    borderWidth: 4,
  },
}
