open Vitest
open TestingLibraryReact

it("should not change displayName", () => {
  expect(Text.make->React.displayName)->toMatchSnapshot
})

it("should render a visible element", () => {
  <Text> {"a custom text children"->React.string} </Text>->render->ignore
  expect(screen->getByTextExn("a custom text children"))->toBeVisible
})

it("should match snapshot with some style", () => {
  let {baseElement} =
    <Text
      style={Style.style(
        ~color="#000",
        ~textDecorationStyle=#double,
        ~textDecorationColor="red",
        ~textDecorationLine=#underline,
        (),
      )}>
      {"a custom text children"->React.string}
    </Text>->render

  expect(baseElement)->toMatchSnapshot
})
