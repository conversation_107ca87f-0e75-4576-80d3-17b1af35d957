// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`should match snapshot with some style 1`] = `
<body>
  <div>
    <div
      class="css-text-901oao"
      dir="auto"
      style="color: rgb(0, 0, 0); text-decoration-color: rgba(255,0,0,1.00); text-decoration: underline; text-decoration-style: double; word-break: breakWord;"
    >
      a custom text children
    </div>
  </div>
</body>
`;

exports[`should not change displayName 1`] = `"Text"`;
