open Vitest
open TestingLibraryReact

it("should not change displayName", () => {
  expect(View.make->React.displayName)->toMatchSnapshot
})

it("should render a visible element", () => {
  render(
    <View>
      <Text> {"a custom text children"->React.string} </Text>
    </View>,
  )->ignore

  let viewElement = screen->getByTextExn("a custom text children")

  expect(viewElement)->toBeVisible
})

it("should match snapshot with some style", () => {
  let {baseElement} = render(
    <View style={Style.style(~borderColor="#000", ~borderWidth=1., ())}>
      <Text> {"a custom text children"->React.string} </Text>
    </View>,
  )

  expect(baseElement)->toMatchSnapshot
})

it("should assign a HTMLDivElement ref", () => {
  let spyRef = fn1(ignore)

  render(<View ref={spyRef->fn} />)->ignore

  expect(spyRef)->toHaveBeenCalledTimes(1)

  let spyRefMockCall1 = spyRef->calls1->Array.getExn(0)->Array.getExn(0)
  let refTypeClassify = switch spyRefMockCall1->Js.Types.classify {
  | JSObject(_) if %raw(`spyRefMockCall1 instanceof HTMLDivElement`) => Some("HTMLDivElement")
  | _ => None
  }

  expect(refTypeClassify)->toStrictEqual(Some("HTMLDivElement"))
})
