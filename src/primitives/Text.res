open Style

@react.component
let make = React.forwardRef((~children, ~style=?, ref) => {
  let ref = Js.Nullable.toOption(ref)->Option.map(ReactNative.Ref.value)
  let style =
    [Some(ReactNative.Style.unsafeStyle({"wordBreak": "breakWord"})), style]->arrayOptionStyle

  // TODO — Use a simple <span /> dom element and remove react-native-web dep
  <ReactNative.Text ?ref style> children </ReactNative.Text>
})

React.setDisplayName(make, "Text")
