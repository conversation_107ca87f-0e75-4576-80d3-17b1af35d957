// > https://blog.vjeux.com/2014/javascript/react-css-in-js-nationjs.html
// Today, what is the best solution to implement css in js ?
// It's hard to say. Today, we use the react-native-web implementation which works well.
// It supports all long-form CSS properties. But there is no direct support for @-rules,
// selectors, pseudo-selectors, pseudo-elements …
// With react-native-web@18, StyleSheet has been rewritten using https://github.com/necolas/styleq.
// It has major perf impprovements and some new features. To investigate later.

type t = ReactNative.Style.t
type size = ReactNative.Style.size

// TODO - the difference between ViewStyle and TextStyle does not exist today,
// so there is a risk of errors at runtime
let style = ReactNative.Style.style
let dp = ReactNative.Style.dp
let pct = ReactNative.Style.pct
let auto = ReactNative.Style.auto
let arrayOptionStyle = ReactNative.Style.arrayOption
let arrayStyle = ReactNative.Style.array
let merge = ReactNative.StyleSheet.flatten
let shadowOffset = ReactNative.Style.offset

external toReactDOMStyle: ReactNative.Style.t => ReactDOM.style = "%identity"

module Color = {
  type t = string
}
