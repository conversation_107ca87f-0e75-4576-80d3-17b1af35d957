@react.component
let make = React.forwardRef((~children=?, ~style=?, ref) => {
  let ref = Js.Nullable.toOption(ref)->Option.map(ReactNative.Ref.value)
  // TODO — Use a simple <div /> dom element and remove react-native-web dep
  switch children {
  | Some(children) => <ReactNative.View ?ref ?style> children </ReactNative.View>
  | _ => <ReactNative.View ?ref ?style />
  }
})

let make = React.memo(make)

React.setDisplayName(make, "View")
