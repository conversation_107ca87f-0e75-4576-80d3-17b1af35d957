// > https://blog.vjeux.com/2014/javascript/react-css-in-js-nationjs.html
// Today, what is the best solution to implement css in js ?
// It's hard to say. Today, we use the react-native-web implementation which works well.
// It supports all long-form CSS properties. But there is no direct support for @-rules,
// selectors, pseudo-selectors, pseudo-elements …
// With react-native-web@18, StyleSheet has been rewritten using https://github.com/necolas/styleq.
// It has major perf impprovements and some new features. To investigate later.

type t = ReactNative.Style.t
type size = ReactNative.Style.size

module Color: {
  @genType type t = string
}

// TODO - the difference between ViewStyle and TextStyle does not exist today,
// so there is a risk of errors at runtime
@genType
let style: (
  ~resizeMode: ReactNative.Style.resizeMode=?,
  ~overlayColor: Color.t=?,
  ~tintColor: Color.t=?,
  ~color: Color.t=?,
  ~fontFamily: string=?,
  ~fontSize: float=?,
  ~fontStyle: ReactNative.Style.fontStyle=?,
  ~fontVariant: array<ReactNative.FontVariant.t>=?,
  ~fontWeight: [
    | #_100
    | #_200
    | #_300
    | #_400
    | #_500
    | #_600
    | #_700
    | #_800
    | #_900
    | #bold
    | #normal
  ]=?,
  ~includeFontPadding: bool=?,
  ~letterSpacing: float=?,
  ~lineHeight: float=?,
  ~textAlign: ReactNative.Style.textAlign=?,
  ~textAlignVertical: ReactNative.Style.textAlignVertical=?,
  ~textDecorationColor: Color.t=?,
  ~textDecorationLine: [
    | #lineThrough
    | #none
    | #underline
    | #underlineLineThrough
  ]=?,
  ~textDecorationStyle: ReactNative.Style.textDecorationStyle=?,
  ~textShadowColor: Color.t=?,
  ~textShadowOffset: ReactNative.Style.offset=?,
  ~textShadowRadius: float=?,
  ~textTransform: ReactNative.Style.textTransform=?,
  ~writingDirection: ReactNative.Style.writingDirection=?,
  ~backfaceVisibility: ReactNative.Style.backfaceVisibility=?,
  ~backgroundColor: Color.t=?,
  ~borderBottomColor: Color.t=?,
  ~borderBottomEndRadius: float=?,
  ~borderBottomLeftRadius: float=?,
  ~borderBottomRightRadius: float=?,
  ~borderBottomStartRadius: float=?,
  ~borderBottomWidth: float=?,
  ~borderColor: Color.t=?,
  ~borderEndColor: Color.t=?,
  ~borderEndWidth: float=?,
  ~borderLeftColor: Color.t=?,
  ~borderLeftWidth: float=?,
  ~borderRadius: float=?,
  ~borderRightColor: Color.t=?,
  ~borderRightWidth: float=?,
  ~borderStartColor: Color.t=?,
  ~borderStartWidth: float=?,
  ~borderStyle: ReactNative.Style.borderStyle=?,
  ~borderTopColor: Color.t=?,
  ~borderTopEndRadius: float=?,
  ~borderTopLeftRadius: float=?,
  ~borderTopRightRadius: float=?,
  ~borderTopStartRadius: float=?,
  ~borderTopWidth: float=?,
  ~borderWidth: float=?,
  ~elevation: float=?,
  ~opacity: float=?,
  ~transform: array<ReactNative.Style.transform>=?,
  ~shadowColor: Color.t=?,
  ~shadowOffset: ReactNative.Style.offset=?,
  ~shadowOpacity: float=?,
  ~shadowRadius: float=?,
  ~alignContent: [
    | #center
    | #flexEnd
    | #flexStart
    | #spaceAround
    | #spaceBetween
    | #stretch
  ]=?,
  ~alignItems: [#baseline | #center | #flexEnd | #flexStart | #stretch]=?,
  ~alignSelf: [#auto | #baseline | #center | #flexEnd | #flexStart | #stretch]=?,
  ~aspectRatio: float=?,
  ~bottom: size=?,
  ~direction: [#inherit_ | #ltr | #rtl]=?,
  ~display: ReactNative.Style.display=?,
  ~_end: size=?,
  ~flex: float=?,
  ~flexBasis: ReactNative.Style.margin=?,
  ~flexDirection: [#column | #columnReverse | #row | #rowReverse]=?,
  ~flexGrow: float=?,
  ~flexShrink: float=?,
  ~flexWrap: ReactNative.Style.flexWrap=?,
  ~height: size=?,
  ~justifyContent: [
    | #center
    | #flexEnd
    | #flexStart
    | #spaceAround
    | #spaceBetween
    | #spaceEvenly
  ]=?,
  ~left: size=?,
  ~margin: ReactNative.Style.margin=?,
  ~marginBottom: ReactNative.Style.margin=?,
  ~marginEnd: ReactNative.Style.margin=?,
  ~marginHorizontal: ReactNative.Style.margin=?,
  ~marginLeft: ReactNative.Style.margin=?,
  ~marginRight: ReactNative.Style.margin=?,
  ~marginStart: ReactNative.Style.margin=?,
  ~marginTop: ReactNative.Style.margin=?,
  ~marginVertical: ReactNative.Style.margin=?,
  ~maxHeight: size=?,
  ~maxWidth: size=?,
  ~minHeight: size=?,
  ~minWidth: size=?,
  ~overflow: ReactNative.Style.overflow=?,
  ~padding: size=?,
  ~paddingBottom: size=?,
  ~paddingEnd: size=?,
  ~paddingHorizontal: size=?,
  ~paddingLeft: size=?,
  ~paddingRight: size=?,
  ~paddingStart: size=?,
  ~paddingTop: size=?,
  ~paddingVertical: size=?,
  ~position: ReactNative.Style.position=?,
  ~right: size=?,
  ~start: size=?,
  ~top: size=?,
  ~width: size=?,
  ~zIndex: int=?,
  unit,
) => t

let dp: float => size
let pct: float => size
let auto: ReactNative.Style.margin
let arrayOptionStyle: array<option<t>> => t
let arrayStyle: array<t> => t
let merge: array<t> => t
let shadowOffset: (~height: float, ~width: float) => ReactNative.Style.offset

let toReactDOMStyle: t => ReactDOM.style
