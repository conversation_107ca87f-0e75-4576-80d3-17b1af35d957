// NOTE - The layout system is almost complete.
// However, there are one concern about <Columns /> component:
// + how to manage the responsive ?
// + add collapseBelow prop ?

// REVIEW - this component may be replaced by Group logic
// which handles better the responsiveness and is more flexible.

open Style

let styles = StyleSheet.create({
  "container": style(~flexDirection=#row, ~alignItems=#center, ~width=100.->pct, ()),
  "alignItemsStart": style(~alignItems=#flexStart, ()),
  "alignItemsCenter": style(~alignItems=#center, ()),
  "alignItemsEnd": style(~alignItems=#flexEnd, ()),
  "columnView": style(),
})

let containerStyleFromParams = (~direction, ~align) =>
  [
    style(~flexDirection=direction ? #column : #row, ()),
    switch align {
    | #start => styles["alignItemsStart"]
    | #center => styles["alignItemsCenter"]
    | #end => styles["alignItemsEnd"]
    | _ => styles["alignItemsStart"]
    },
  ]->arrayStyle

let itemStyleFromParams = (~width, ~height, ~space, ~isLastItem, ~isContainerColumned) => {
  let height = switch height {
  | Some(height) => `${Float.toString(height *. 100.)}%`
  | None => "auto"
  }

  if isLastItem && !isContainerColumned {
    style(~flex=width, ())->ReactNative.Style.unsafeAddStyle({"height": height})
  } else {
    [
      style(~flex=width, ())->ReactNative.Style.unsafeAddStyle({"height": height}),
      switch space {
      | #xxsmall => style(~paddingRight=Spaces.xxsmall->dp, ())
      | #xsmall => style(~paddingRight=Spaces.xsmall->dp, ())
      | #small => style(~paddingRight=Spaces.small->dp, ())
      | #normal => style(~paddingRight=Spaces.normal->dp, ())
      | #medium => style(~paddingRight=Spaces.medium->dp, ())
      | #large => style(~paddingRight=Spaces.large->dp, ())
      | #xlarge => style(~paddingRight=Spaces.xlarge->dp, ())
      | #xxlarge => style(~paddingRight=Spaces.xxlarge->dp, ())
      | #huge => style(~paddingRight=Spaces.huge->dp, ())
      | #xhuge => style(~paddingRight=Spaces.xhuge->dp, ())
      | _ => styles["columnView"]
      },
    ]->arrayStyle
  }
}

type alignment = [#start | #end | #center]

let getColumnWidth: React.element => option<
  Column.proportion,
> = %raw(` function(children) { return children.props.width } `)
let getColumnHeight: React.element => option<
  Column.proportion,
> = %raw(` function(children) { return children.props.height } `)

let getProportion = proportion =>
  switch proportion {
  | #fluid => 1.
  | #content => -1. // TODO - to remove?
  | #half => 0.5
  | #one_third => 0.33
  | #quarter => 0.25
  | #two_third => 0.67
  | #three_quarter => 0.75
  }

@react.component
let make = (~children, ~space=#none, ~align=#start) => {
  let childrenAsArray = children->React.Children.toArray

  let isContainerColumned =
    childrenAsArray
    ->Array.map(element => getColumnWidth(element)->Option.mapWithDefault(-1., getProportion))
    ->Array.reduce(0., \"+.")
    ->Float.toInt === childrenAsArray->Array.length

  <View
    style={[
      styles["container"],
      containerStyleFromParams(~direction=isContainerColumned, ~align),
    ]->arrayStyle}>
    {childrenAsArray
    ->Array.mapWithIndex((index, item) =>
      <View
        key={"column" ++ Int.toString(index)}
        style={itemStyleFromParams(
          ~width=getColumnWidth(item)->Option.mapWithDefault(-1., getProportion),
          ~height=getColumnHeight(item)->Option.map(getProportion),
          ~space,
          ~isLastItem=index === childrenAsArray->Array.length - 1,
          ~isContainerColumned,
        )}>
        item
      </View>
    )
    ->React.array}
  </View>
}
