type alignment = [#auto | #stretch | #start | #center | #end]

@genType
module Span: {
  @genType type tone = [#neutral | #faded | #success]

  @genType @react.component
  let make: (
    ~text: string,
    ~tone: tone=?,
    ~italic: bool=?,
    ~bold: bool=?,
    ~size: FontSizes.t=?,
  ) => React.element
}

@genType
module Gap: {
  @genType @react.component
  let make: (~space: Spaces.t=?) => React.element
}

@genType @react.component
let make: (
  ~children: React.element=?,
  ~arrowed: bool=?,
  ~placement: ReactAria.Overlay.Position.placement=?,
  ~offset: float=?,
  ~crossOffset: float=?,
  ~content: React.element,
  ~disabled: bool=?,
  ~grow: bool=?,
  ~delay: int=?,
  ~closeDelay: int=?,
  ~triggerAlign: alignment=?,
  ~opened: bool=?,
  ~onOpenChange: bool => unit=?,
) => React.element
