open StyleX
open Intl
open WebAPI

let styles = StyleX.create({
  "root": style(~maxWidth="550px", ()),
  "DialogHeader_root": style(
    ~display=#flex,
    ~gap="6px",
    ~padding=`${Spaces.normalPx} ${Spaces.largePx}`,
    ~paddingBottom=Spaces.xnormalPx,
    ~borderBottom="1px solid " ++ Colors.neutralColor20,
    (),
  ),
  "DialogHeader_title": style(~font=`normal 700 16px "Archivo"`, ~color=Colors.neutralColor90, ()),
  "DialogContent_root": style(
    ~display=#flex,
    ~flexDirection=#column,
    ~overflow=#auto,
    ~maxHeight="60vh",
    ~gap=Spaces.normalPx,
    ~paddingTop=Spaces.normalPx,
    ~paddingBottom=Spaces.largePx,
    (),
  ),
  "DialogContent_scrollGradient": style(
    ~position=#absolute,
    ~bottom="0",
    ~left="0",
    ~right="0",
    ~zIndex=1,
    ~pointerEvents=#none,
    ~height="13px",
    ~backgroundImage="radial-gradient(ellipse at center, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0) 75%)",
    ~backgroundSize="100% 200%",
    ~backgroundPosition="center top",
    (),
  ),
  "DialogFooter_root": style(
    ~display=#flex,
    ~justifyContent=#"flex-end",
    ~borderTop="1px solid " ++ Colors.neutralColor20,
    ~gap="6px",
    ~padding=`${Spaces.xnormalPx} ${Spaces.mediumPx}`,
    (),
  ),
})

module Section = {
  module HeaderTooltipIcon = {
    @react.component
    let make = React.memo((~content, ~titleWrapperRef) =>
      <div
        style={ReactDOMStyle.make(
          ~marginRight="13px",
          ~transform="scale(0.9)",
          ~opacity="0.75",
          (),
        )}>
        <Offset top={-0.5}>
          <TooltipIcon altTriggerRef=titleWrapperRef offset=3.> {content} </TooltipIcon>
        </Offset>
      </div>
    )
  }

  let styles = StyleX.create({
    "Section_root": style(
      ~display=#flex,
      ~flexDirection=#column,
      ~padding=`${Spaces.xsmallPx} ${Spaces.largePx} 6px ${Spaces.largePx}`,
      ~gap=Spaces.normalPx,
      (),
    ),
    "Section_header": style(~display=#flex, ~gap=Spaces.xsmallPx, ()),
    "Section_headerHoverable": style(
      ~\":hover"=style(~filter="drop-shadow(0.5px 0 0 #00000080)", ()),
      (),
    ),
    "Section_headerDivider": style(
      ~flex="1",
      ~alignSelf=#center,
      ~height="0.5px",
      ~backgroundColor=Colors.neutralColor20,
      (),
    ),
    "Section_titleWrapper": style(~display=#contents, ()),
    "Section_title": style(
      ~font=`normal 400 12px "Libre Franklin"`,
      ~color=Colors.neutralColor90,
      (),
    ),
    "Section_content": style(
      ~display=#flex,
      ~flexDirection=#inherit,
      ~gap="inherit",
      ~overflow=#hidden,
      ~transition="all .15s cubic-bezier(.4, 0, .2, 1)",
      (),
    ),
  })

  let styleProps = () => StyleX.props([styles["Section_root"]])
  let headerStyleProps = (~hoverable) =>
    StyleX.props([
      styles["Section_header"],
      hoverable ? styles["Section_headerHoverable"] : styles["none"],
    ])
  let headerDividerStyleProps = () => StyleX.props([styles["Section_headerDivider"]])
  let titleStyleProps = () => StyleX.props([styles["Section_title"]])
  let titleWrapperStyleProps = () => StyleX.props([styles["Section_titleWrapper"]])
  let styleContentProps = (~hidden, ~collapsable) => {
    let maxHeight = {
      let defaultMaxHeight = collapsable ? "300px" : "auto"
      hidden ? "0" : defaultMaxHeight
    }
    StyleX.props([
      styles["Section_content"],
      style(~maxHeight, ~opacity=hidden ? 0. : 1., ()),
      style(~marginBottom=hidden ? `-${Spaces.xlargePx}` : "0", ()),
    ])
  }

  @react.component
  let make = React.memo((
    ~children,
    ~title=?,
    ~tooltip=?,
    ~collapsable=false,
    ~defaultCollapsed=false,
  ) => {
    let titleWrapperRef = React.useRef(Js.Nullable.null)
    let (opened, setOpened) = React.useState(() => !defaultCollapsed || !collapsable)

    let {?style, ?className} = styleProps()
    let {style: ?headerStyle, className: ?headerClassName} = headerStyleProps(
      ~hoverable=collapsable,
    )
    let {className: ?headerDividerClassName} = headerDividerStyleProps()
    let {style: ?titleStyle, className: ?titleClassName} = titleStyleProps()
    let {style: ?titleWrapperStyle, className: ?titleWrapperClassName} = titleWrapperStyleProps()
    let {style: ?contentStyle, className: ?contentClassName} = styleContentProps(
      ~hidden=!opened,
      ~collapsable,
    )

    <div ?style ?className>
      {switch title {
      | Some(title) =>
        <Touchable disabled={!collapsable} onPress={_ => setOpened(v => !v)}>
          <div style=?headerStyle className=?headerClassName>
            {if collapsable {
              <Icon
                name={opened ? #arrow_down_light : #arrow_up_light}
                fill=Colors.neutralColor90
                size=13.
              />
            } else {
              React.null
            }}
            <div
              ref={titleWrapperRef->ReactDOM.Ref.domRef}
              style=?titleWrapperStyle
              className=?titleWrapperClassName>
              <span style=?titleStyle className=?titleClassName> {title->React.string} </span>
              {switch tooltip {
              | Some(content) => <HeaderTooltipIcon content titleWrapperRef />
              | None => React.null
              }}
              <div className=?headerDividerClassName />
            </div>
          </div>
        </Touchable>
      | None => React.null
      }}
      <div style=?contentStyle className=?contentClassName> children </div>
    </div>
  })
}

module DialogHeader = {
  let styleProps = () => StyleX.props([styles["DialogHeader_root"]])
  let titleStyleProps = () => StyleX.props([styles["DialogHeader_title"]])

  @react.component
  let make = React.memo((~title, ~titleStartElement=React.null) => {
    let {?style, ?className} = styleProps()
    let {style: ?titleStyle, className: ?titleClassName} = titleStyleProps()

    <div ?style ?className>
      <span style=?titleStyle className=?titleClassName> {title->React.string} </span>
      {titleStartElement}
    </div>
  })
}

module DialogContent = {
  let styleProps = () => StyleX.props([styles["DialogContent_root"]])
  let scrollGradientStyleProps = () => StyleX.props([styles["DialogContent_scrollGradient"]])

  @react.component
  let make = React.memo((~children) => {
    let contentRef = React.useRef(Js.Nullable.null)
    let (contentScrollable, setContentScrollable) = React.useState(() => false)

    let handleScrollable = React.useCallback1(() =>
      switch contentRef.current->Js.Nullable.toOption {
      | Some(domElement) =>
        setContentScrollable(
          _ => domElement->DomElement.scrollHeight > domElement->DomElement.clientHeight,
        )
      | None => ()
      }
    , [contentRef])

    React.useLayoutEffect1(() => {
      handleScrollable()
      None
    }, [contentRef])

    ReactAria.Resize.useObserver({
      ref: contentRef->ReactDOM.Ref.domRef,
      onResize: handleScrollable,
    })

    let onScroll = React.useCallback0(event => {
      let domElement =
        event
        ->ReactEvent.UI.currentTarget
        ->ReactDomEventTarget.toUnsafeDomEventTarget
        ->EventTarget.unsafeAsDomElement
      let hasReachedBottomScroll =
        domElement->DomElement.scrollTop->Float.toInt + domElement->DomElement.clientHeight >=
          domElement->DomElement.scrollHeight - 10

      setContentScrollable(_ => !hasReachedBottomScroll)
    })

    let {?style, ?className} = styleProps()
    let {
      style: ?scrollGradientStyle,
      className: ?scrollGradientClassName,
    } = scrollGradientStyleProps()

    <div style={ReactDOM.Style.make(~position="relative", ())}>
      <div onScroll ref={contentRef->ReactDOM.Ref.domRef} ?style ?className> {children} </div>
      <AnimatedRender displayed={contentScrollable} animation=#fade duration=450>
        <div style=?scrollGradientStyle className=?scrollGradientClassName />
      </AnimatedRender>
    </div>
  })
}

module DialogFooter = {
  let styleProps = () => StyleX.props([styles["DialogFooter_root"]])

  @react.component
  let make = React.memo((
    ~commitButtonText,
    ~commitLoading,
    ~commitDisabled,
    ~onRequestCommit,
    ~onRequestDialogClose,
  ) => {
    let {?style, ?className} = styleProps()

    <div ?style ?className>
      <Button size=#tiny variation=#neutral onPress={_ => onRequestDialogClose()}>
        {t("Discard")->React.string}
      </Button>
      <Button
        size=#tiny
        variation=#success
        loading=commitLoading
        disabled=commitDisabled
        onPress={_ => onRequestCommit()}>
        {commitButtonText->React.string}
      </Button>
    </div>
  })
}

module Tabs = {
  let styles = StyleX.create({
    "Tabs_root": style(
      ~display=#flex,
      ~flexDirection=#row,
      ~backgroundColor=Colors.neutralColor05,
      ~boxSizing=#"border-box",
      (),
    ),
    "Tabs_Tab_root": style(
      ~position=#relative,
      ~display=#flex,
      ~justifyContent=#center,
      ~alignItems=#center,
      ~width="100%",
      ~height="40px",
      ~cursor=#pointer,
      ~padding=`0 ${Spaces.largePx}`,
      ~border="1px solid " ++ Colors.neutralColor20,
      ~borderTop="none",
      ~boxSizing=#"border-box",
      ~whiteSpace=#nowrap,
      ~font=`normal 500 16px "Archivo"`,
      ~color=Colors.neutralColor90,
      ~transition="box-shadow .05s ease-out",
      ~\":hover"=style(~boxShadow=`inset 0 -1px 5px 0 ${Colors.neutralColor15}`, ()),
      (),
    ),
    "Tabs_Tab_selected": style(
      ~backgroundColor=Colors.neutralColor00,
      ~borderBottom="none",
      ~borderTopLeftRadius="5px",
      ~borderTopRightRadius="5px",
      ~fontWeight=#700,
      ~cursor=#default,
      ~\":hover"=style(~boxShadow="none", ()),
      (),
    ),
    "Tabs_Tab_disabled": style(
      ~backgroundColor=Colors.neutralColor10,
      ~color=Colors.neutralColor25,
      ~cursor=#default,
      ~\":hover"=style(~boxShadow="none", ()),
      (),
    ),
  })

  module Tab = {
    let styleProps = (
      ~selected,
      ~disabled,
      ~hasLeftSibling,
      ~hasRightSibling,
      ~hasLeftSiblingSelected,
      ~hasRightSiblingSelected,
    ) => {
      let removeLeftBorder =
        !hasLeftSibling ||
        !selected ||
        !hasRightSibling && hasLeftSiblingSelected ||
        (!hasLeftSibling && hasRightSiblingSelected)

      let removeRightBorder = !hasRightSibling || hasRightSiblingSelected

      StyleX.props([
        styles["Tabs_Tab_root"],
        selected ? styles["Tabs_Tab_selected"] : style(~backgroundColor=Colors.neutralColor05, ()),
        disabled ? styles["Tabs_Tab_disabled"] : style(),
        removeLeftBorder ? style(~borderLeft="none", ()) : style(),
        removeRightBorder ? style(~borderRight="none", ()) : style(),
      ])
    }

    @react.component
    let make = React.memo((~item, ~state) => {
      let {ReactStately.Collection.key: key, rendered} = item
      let ref = React.useRef(Js.Nullable.null)->ReactDOM.Ref.domRef
      let {tabProps, selected, disabled} = ReactAria.Tab.use(~props={key: key}, ~state, ~ref)

      let (hasLeftSibling, hasRightSibling) = (
        item.prevKey->Js.Nullable.toOption->Option.isSome,
        item.nextKey->Js.Nullable.toOption->Option.isSome,
      )
      let (hasLeftSiblingSelected, hasRightSiblingSelected) = (
        item.prevKey === state.selectedKey,
        item.nextKey === state.selectedKey,
      )

      let {?style, ?className} = styleProps(
        ~selected,
        ~disabled,
        ~hasLeftSibling,
        ~hasRightSibling,
        ~hasLeftSiblingSelected,
        ~hasRightSiblingSelected,
      )

      <ReactAria.Focus.Scope autoFocus={!hasLeftSibling} restoreFocus=true>
        <Tooltip
          grow=true
          content={<Tooltip.Span text={item.props.tooltipText->Option.getWithDefault("")} />}
          disabled={item.props.tooltipText->Option.isNone}
          placement=#"top start"
          offset={-8.}
          delay=0>
          <ReactAria.Spread props=tabProps>
            <div ref ?style ?className> {rendered} </div>
          </ReactAria.Spread>
        </Tooltip>
      </ReactAria.Focus.Scope>
    })
  }

  module TabPanel = {
    @react.component
    let make = React.memo((~state, ~render) => {
      let props = {ReactAria.Tab.Panel.\"aria-describedby": ""} // NOTE - crash if not defined
      let ref = React.useRef(Js.Nullable.null)->ReactDOM.Ref.domRef
      let {tabPanelProps} = ReactAria.Tab.Panel.use(~props, ~state, ~ref, ())

      let tabContent = React.useMemo1(
        () =>
          state.selectedItem
          ->Js.Nullable.toOption
          ->Option.mapWithDefault(React.null, item => item.props.children),
        [state.selectedItem],
      )

      <ReactAria.Spread props=tabPanelProps>
        <div ref> {render(~tabContent)} </div>
      </ReactAria.Spread>
    })
  }

  let styleProps = () => StyleX.props([styles["Tabs_root"]])

  @react.component
  let make = React.memo((~children as renderTabPanel, ~items, ~disabledKeys=?, ~onChange=?) => {
    let props = {
      ReactStately.TabList.children: items,
      \"aria-label": "popover-dialog-tabs",
      ?disabledKeys,
      onSelectionChange: key =>
        switch (key->Js.Nullable.toOption, onChange) {
        | (Some(key), Some(onChange)) => onChange(key)
        | _ => ()
        },
    }
    let state = ReactStately.TabList.useState(~props)
    let ref = React.useRef(Js.Nullable.null)->ReactDOM.Ref.domRef
    let {tabListProps} = ReactAria.Tab.List.use(~props, ~state, ~ref)

    let {?style, ?className} = styleProps()

    <>
      <ReactAria.Spread props=tabListProps>
        <div ref ?style ?className>
          {state.collection
          ->ReactStately.Collection.toArray
          ->Array.map(item => <Tab key=item.key item state />)
          ->React.array}
        </div>
      </ReactAria.Spread>
      <TabPanel
        key={state.selectedItem->Js.Nullable.toOption->Option.mapWithDefault("", item => item.key)}
        render=renderTabPanel
        state
      />
    </>
  })
}

module Tab = ReactStately.Collection.Item

let styleProps = (~width) =>
  StyleX.props([
    styles["root"],
    switch width {
    | Some(width) => style(~width=Float.toString(width) ++ "px", ())
    | None => style()
    },
  ])

type headerTitle = {text: string, titleStartElement?: React.element}
type headerTab = {disabledKeys?: array<ReactStately.key>, onChange?: ReactStately.key => unit}
type header = Title(headerTitle) | Tabs(headerTab)

@react.component
let make = (
  ~ariaProps,
  ~children,
  ~width=?,
  ~header,
  ~commitDisabled=false,
  ~commitLoading=false,
  ~commitButtonText=t("Save"),
  ~onRequestCommit,
  ~onRequestClose,
) => {
  let onKeyDown = React.useCallback2(event => {
    let ctrlKeyPressed = KeyboardEvent.ctrlKey(event)
    switch WebAPI.KeyboardEvent.key(event) {
    | "Enter" if ctrlKeyPressed && !commitDisabled =>
      onRequestCommit()
      event->WebAPI.KeyboardEvent.preventDefault
    | "Escape" => onRequestClose()
    | _ => ()
    }
  }, (onRequestCommit, commitDisabled))

  React.useEffect1(() => {
    let document = WebAPI.document->WebAPI.Document.asDomElement
    document->WebAPI.DomElement.addKeyDownEventListener(onKeyDown)
    Some(_ => document->WebAPI.DomElement.removeKeyDownEventListener(onKeyDown))
  }, [onKeyDown])

  let {?style, ?className} = styleProps(~width)

  switch header {
  | Title({text: title, ?titleStartElement}) =>
    <Popover.Dialog ariaLabel=title ariaProps ?style ?className>
      <DialogHeader title ?titleStartElement />
      <DialogContent> {children} </DialogContent>
      <DialogFooter
        commitButtonText
        commitLoading
        commitDisabled
        onRequestCommit
        onRequestDialogClose=onRequestClose
      />
    </Popover.Dialog>
  | Tabs({?disabledKeys, ?onChange}) =>
    <Popover.Dialog ariaProps ?style ?className>
      <Tabs items=children ?onChange ?disabledKeys>
        {(~tabContent) => <>
          <DialogContent> {tabContent} </DialogContent>
          <DialogFooter
            commitButtonText
            commitLoading
            commitDisabled
            onRequestCommit
            onRequestDialogClose=onRequestClose
          />
        </>}
      </Tabs>
    </Popover.Dialog>
  }
}

let make = React.memo(make)
